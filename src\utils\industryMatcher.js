/**
 * Industry Matcher Utility
 * Calculates industry compatibility based on RIASEC, VIA, and OCEAN assessment data
 */

// Industry data based on INDUSTRY.md
export const industriesData = {
  teknologi: {
    name: "Teknologi",
    icon: "💻",
    riasec: { investigative: 50, realistic: 30, conventional: 20 },
    via: { loveOfLearning: 30, curiosity: 30, perseverance: 20, creativity: 20 },
    ocean: { openness: 60, conscientiousness: 40 },
    description: "Industri yang sangat bergantung pada analisis (Investigative), namun juga membutuhkan keterampilan praktis (Realistic) dan kepatuhan pada prosedur (Conventional)."
  },
  kesehatan: {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    icon: "🏥",
    riasec: { investigative: 50, social: 50 },
    via: { kindness: 40, judgment: 30, zest: 20, loveOfLearning: 10 },
    ocean: { conscientiousness: 60, agreeableness: 40 },
    description: "Keseimbangan antara kemampuan diagnostik (Investigative) dan interaksi empatik dengan pasien (Social) adalah inti dari industri ini."
  },
  keuangan: {
    name: "<PERSON><PERSON><PERSON>",
    icon: "💰",
    riasec: { conventional: 60, enterprising: 40 },
    via: { prudence: 40, judgment: 30, fairness: 20, leadership: 10 },
    ocean: { conscientiousness: 70, lowNeuroticism: 30 },
    description: "Kepatuhan pada aturan dan ketelitian (Conventional) menjadi fondasi, didukung oleh kemampuan manajerial dan persuasif (Enterprising)."
  },
  pendidikan: {
    name: "Pendidikan",
    icon: "🎓",
    riasec: { social: 60, artistic: 40 },
    via: { loveOfLearning: 30, socialIntelligence: 30, leadership: 20, creativity: 20 },
    ocean: { extraversion: 50, agreeableness: 50 },
    description: "Fokus utama adalah pada interaksi dan pengembangan orang lain (Social), dengan porsi kreativitas yang signifikan dalam penyampaian materi (Artistic)."
  },
  rekayasa: {
    name: "Rekayasa",
    icon: "⚙️",
    riasec: { realistic: 60, investigative: 40 },
    via: { perseverance: 30, teamwork: 30, prudence: 20, creativity: 20 },
    ocean: { conscientiousness: 80, lowNeuroticism: 20 },
    description: "Pekerjaan praktis dan teknis (Realistic) mendominasi, didukung oleh perencanaan dan analisis yang mendalam (Investigative)."
  },
  pemasaran: {
    name: "Pemasaran",
    icon: "📈",
    riasec: { enterprising: 50, artistic: 50 },
    via: { creativity: 40, socialIntelligence: 30, zest: 20, perspective: 10 },
    ocean: { extraversion: 60, openness: 40 },
    description: "Kombinasi seimbang antara kemampuan persuasif untuk bisnis (Enterprising) dan ide-ide kreatif untuk kampanye (Artistic)."
  },
  hukum: {
    name: "Hukum",
    icon: "⚖️",
    riasec: { investigative: 50, enterprising: 50 },
    via: { judgment: 40, fairness: 30, perseverance: 30 },
    ocean: { conscientiousness: 70, lowNeuroticism: 30 },
    description: "Membutuhkan porsi yang sama antara analisis kasus yang mendalam (Investigative) dan kemampuan berargumen serta memimpin (Enterprising)."
  },
  kreatif: {
    name: "Kreatif",
    icon: "🎨",
    riasec: { artistic: 70, realistic: 30 },
    via: { creativity: 50, appreciationOfBeauty: 30, bravery: 10, zest: 10 },
    ocean: { openness: 80, lowConscientiousness: 20 },
    description: "Didominasi oleh ekspresi diri dan ide-ide baru (Artistic), dengan elemen praktis dalam eksekusi karya (Realistic)."
  },
  media: {
    name: "Media",
    icon: "📺",
    riasec: { artistic: 40, social: 30, enterprising: 30 },
    via: { creativity: 40, socialIntelligence: 30, curiosity: 30 },
    ocean: { extraversion: 50, openness: 50 },
    description: "Perpaduan antara pembuatan konten (Artistic), interaksi dengan audiens (Social), dan tujuan bisnis (Enterprising)."
  },
  penjualan: {
    name: "Penjualan",
    icon: "🤝",
    riasec: { enterprising: 70, social: 30 },
    via: { zest: 30, socialIntelligence: 30, perseverance: 20, hope: 20 },
    ocean: { extraversion: 70, conscientiousness: 30 },
    description: "Sangat berorientasi pada persuasi dan pencapaian target (Enterprising), dengan dukungan kemampuan interpersonal (Social)."
  },
  sains: {
    name: "Sains",
    icon: "🔬",
    riasec: { investigative: 100 },
    via: { curiosity: 40, loveOfLearning: 30, perseverance: 20, hope: 10 },
    ocean: { openness: 60, conscientiousness: 40 },
    description: "Industri ini murni tentang penelitian, analisis, dan penemuan, menjadikannya ranah Investigative seutuhnya."
  },
  manufaktur: {
    name: "Manufaktur",
    icon: "🏭",
    riasec: { realistic: 70, conventional: 30 },
    via: { teamwork: 40, perseverance: 30, prudence: 30 },
    ocean: { conscientiousness: 100 },
    description: "Didominasi oleh pekerjaan praktis dengan mesin (Realistic) dalam kerangka kerja yang terstruktur (Conventional). Ketelitian adalah segalanya."
  },
  agrikultur: {
    name: "Agrikultur",
    icon: "🌾",
    riasec: { realistic: 100 },
    via: { perseverance: 50, love: 30, gratitude: 20 },
    ocean: { conscientiousness: 70, lowNeuroticism: 30 },
    description: "Pekerjaan yang sepenuhnya bersifat praktis, langsung, dan berhubungan dengan alam."
  },
  pemerintahan: {
    name: "Pemerintahan",
    icon: "🏛️",
    riasec: { conventional: 50, social: 50 },
    via: { fairness: 40, teamwork: 30, prudence: 20, leadership: 10 },
    ocean: { conscientiousness: 60, agreeableness: 40 },
    description: "Keseimbangan antara pelayanan publik (Social) dan kepatuhan terhadap sistem dan birokrasi (Conventional)."
  },
  konsultasi: {
    name: "Konsultasi",
    icon: "💼",
    riasec: { enterprising: 50, investigative: 50 },
    via: { judgment: 30, perspective: 30, socialIntelligence: 20, leadership: 20 },
    ocean: { extraversion: 40, conscientiousness: 30, openness: 30 },
    description: "Perpaduan seimbang antara analisis masalah (Investigative) dan penyampaian solusi strategis kepada klien (Enterprising)."
  },
  pariwisata: {
    name: "Pariwisata",
    icon: "✈️",
    riasec: { social: 50, enterprising: 30, realistic: 20 },
    via: { socialIntelligence: 40, kindness: 30, zest: 20, teamwork: 10 },
    ocean: { extraversion: 60, agreeableness: 40 },
    description: "Berpusat pada pelayanan tamu (Social), dengan aspek bisnis (Enterprising) dan operasional (Realistic) yang kuat."
  },
  logistik: {
    name: "Logistik",
    icon: "📦",
    riasec: { conventional: 50, realistic: 50 },
    via: { prudence: 50, teamwork: 30, perseverance: 20 },
    ocean: { conscientiousness: 100 },
    description: "Keseimbangan antara pengelolaan data dan jadwal (Conventional) dengan operasional fisik (Realistic). Ketelitian menjadi faktor penentu."
  },
  energi: {
    name: "Energi",
    icon: "⚡",
    riasec: { realistic: 60, investigative: 40 },
    via: { prudence: 40, teamwork: 30, judgment: 30 },
    ocean: { conscientiousness: 80, lowNeuroticism: 20 },
    description: "Pekerjaan teknis di lapangan (Realistic) menjadi mayoritas, didukung oleh analisis sistem yang kompleks (Investigative)."
  },
  sosial: {
    name: "Sosial",
    icon: "🤲",
    riasec: { social: 70, enterprising: 30 },
    via: { kindness: 40, fairness: 30, hope: 20, leadership: 10 },
    ocean: { agreeableness: 60, extraversion: 40 },
    description: "Didorong kuat oleh keinginan membantu orang lain (Social), dengan elemen penggalangan dana dan manajemen (Enterprising)."
  },
  olahraga: {
    name: "Olahraga",
    icon: "⚽",
    riasec: { realistic: 40, social: 30, enterprising: 30 },
    via: { zest: 40, perseverance: 30, teamwork: 20, leadership: 10 },
    ocean: { extraversion: 60, conscientiousness: 40 },
    description: "Melibatkan aktivitas fisik (Realistic), interaksi dengan tim atau klien (Social), dan aspek bisnis kompetitif (Enterprising)."
  },
  properti: {
    name: "Properti",
    icon: "🏠",
    riasec: { enterprising: 100 },
    via: { socialIntelligence: 40, zest: 30, perseverance: 30 },
    ocean: { extraversion: 70, conscientiousness: 30 },
    description: "Industri yang murni berfokus pada penjualan, negosiasi, dan persuasi."
  },
  kuliner: {
    name: "Kuliner",
    icon: "🍳",
    riasec: { artistic: 40, realistic: 30, enterprising: 30 },
    via: { creativity: 40, zest: 30, kindness: 20, teamwork: 10 },
    ocean: { extraversion: 50, agreeableness: 50 },
    description: "Kombinasi dari kreativitas resep (Artistic), proses memasak (Realistic), dan manajemen bisnis (Enterprising)."
  },
  perdagangan: {
    name: "Perdagangan",
    icon: "🛒",
    riasec: { enterprising: 40, conventional: 30, social: 30 },
    via: { socialIntelligence: 40, zest: 30, prudence: 20, fairness: 10 },
    ocean: { extraversion: 60, conscientiousness: 40 },
    description: "Perpaduan antara strategi bisnis (Enterprising), manajemen stok (Conventional), dan layanan pelanggan (Social)."
  },
  telekomunikasi: {
    name: "Telekomunikasi",
    icon: "📡",
    riasec: { realistic: 40, investigative: 30, enterprising: 30 },
    via: { teamwork: 40, perseverance: 30, curiosity: 20, judgment: 10 },
    ocean: { conscientiousness: 50, openness: 50 },
    description: "Melibatkan instalasi infrastruktur (Realistic), pengembangan teknologi (Investigative), dan persaingan bisnis (Enterprising)."
  }
};

// VIA mapping from INDUSTRY.md names to viaIs data keys
export const viaMapping = {
  "Love of Learning": "loveOfLearning",
  "Curiosity": "curiosity",
  "Persistence": "perseverance", // Note: INDUSTRY.md uses "Persistence" but viaIs uses "perseverance"
  "Creativity": "creativity",
  "Kindness": "kindness",
  "Judgment": "judgment",
  "Zest": "zest",
  "Social Intelligence": "socialIntelligence",
  "Teamwork": "teamwork",
  "Fairness": "fairness",
  "Leadership": "leadership",
  "Prudence": "prudence",
  "Perspective": "perspective",
  "Bravery": "bravery",
  "Hope": "hope",
  "Appreciation of Beauty": "appreciationOfBeauty",
  "Gratitude": "gratitude",
  "Love": "love",
  "Humility": "humility",
  "Self Regulation": "selfRegulation"
};

/**
 * Calculate industry compatibility score
 * @param {Object} assessmentData - User's assessment data
 * @param {Object} industry - Industry data
 * @returns {number} - Compatibility score (0-100)
 */
export const calculateIndustryScore = (assessmentData, industry) => {
  if (!assessmentData || !industry) return 0;

  let totalScore = 0;
  let totalWeight = 0;
  let componentCount = 0;

  // Calculate RIASEC score
  if (industry.riasec && assessmentData.riasec) {
    let riasecScore = 0;
    let riasecWeight = 0;

    Object.entries(industry.riasec).forEach(([trait, weight]) => {
      const userScore = assessmentData.riasec[trait] || 0;
      riasecScore += (userScore * weight) / 100;
      riasecWeight += weight;
    });

    if (riasecWeight > 0) {
      totalScore += riasecScore;
      totalWeight += riasecWeight;
      componentCount++;
    }
  }

  // Calculate VIA score
  if (industry.via && assessmentData.viaIs) {
    let viaScore = 0;
    let viaWeight = 0;

    Object.entries(industry.via).forEach(([trait, weight]) => {
      const userScore = assessmentData.viaIs[trait] || 0;
      viaScore += (userScore * weight) / 100;
      viaWeight += weight;
    });

    if (viaWeight > 0) {
      totalScore += viaScore;
      totalWeight += viaWeight;
      componentCount++;
    }
  }

  // Calculate OCEAN score
  if (industry.ocean && assessmentData.ocean) {
    let oceanScore = 0;
    let oceanWeight = 0;

    Object.entries(industry.ocean).forEach(([trait, weight]) => {
      let userScore = 0;

      if (trait === 'lowNeuroticism') {
        userScore = 100 - (assessmentData.ocean.neuroticism || 0);
      } else if (trait === 'lowConscientiousness') {
        userScore = 100 - (assessmentData.ocean.conscientiousness || 0);
      } else {
        userScore = assessmentData.ocean[trait] || 0;
      }

      oceanScore += (userScore * weight) / 100;
      oceanWeight += weight;
    });

    if (oceanWeight > 0) {
      totalScore += oceanScore;
      totalWeight += oceanWeight;
      componentCount++;
    }
  }

  // Normalize score to 0-100 range
  // Only return a score if we have at least one component
  return totalWeight > 0 && componentCount > 0 ? (totalScore / totalWeight) * 100 : 0;
};

/**
 * Get top matching industries for user
 * @param {Object} assessmentData - User's assessment data
 * @param {number} count - Number of top industries to return (default: 4)
 * @returns {Array} - Array of top matching industries with scores
 */
export const getTopMatchingIndustries = (assessmentData, count = 4) => {
  if (!assessmentData) return [];

  const industryScores = Object.entries(industriesData).map(([key, industry]) => ({
    key,
    ...industry,
    score: calculateIndustryScore(assessmentData, industry)
  }));

  return industryScores
    .sort((a, b) => b.score - a.score)
    .slice(0, count);
};
